import LearnTypeEnum from '@/configs/LearnTypeEnum';
import {
  Character,
  CourseEntity,
  DocumentEntity,
  ParagraphEntity,
  SentenceEntity,
  TransactionLearnInfo,
} from 'types/model';
import { LearnStoreProps } from 'types/stores';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export const APP_STORE_NAME = 'entStore';

const useLearnStore = create<LearnStoreProps>()(
  persist(
    (set) => ({
      activeTab: LearnTypeEnum.LISTEN,
      isFinishLearn: false,
      isLoading: false,
      isStartLearn: true,
      currentCourseId: 0,
      currentSentenceId: 0,
      conversations: [],
      speakings: [],
      conversationsByParagraph: [],
      documents: [],
      course: null,
      timeToLearnOneConversation: 0,
      activeDocument: null,
      activeParagraph: undefined,
      showWord: true,
      isLearnAgain: false,
      activeCharacter: null,
      selectedSentence: null,
      paragraphs: [],
      transactionInfo: null,
      sentenceGroupsId: 0,
      exerciseToken: '',
      setTransactionInfo: (transactionInfo: TransactionLearnInfo) => {
        set(() => ({ transactionInfo: transactionInfo }));
      },
      setCharacter: (activeCharacter: Character) => {
        set(() => ({ activeCharacter: activeCharacter }));
      },
      setShowWord: (showWord: boolean) => {
        set(() => ({ showWord: showWord }));
      },
      setLoading: (isLoading: boolean) => {
        set(() => ({ isLoading: isLoading }));
      },
      setCourse: (course: CourseEntity) => {
        set(() => ({ course: course }));
      },

      setExerciseToken: (exerciseToken) => {
        set(() => ({ exerciseToken: exerciseToken }));
      },
      setDocuments: (documents) => {
        set(() => ({ documents: documents }));
      },
      setActiveDocument: (activeDocument: DocumentEntity) => {
        set(() => ({ activeDocument: activeDocument }));
      },
      setActiveParagraph: (activeParagraph: ParagraphEntity | undefined) => {
        set(() => ({ activeParagraph: activeParagraph }));
      },
      setCourseId: (courseId) => {
        set(() => ({ currentCourseId: courseId }));
      },
      setCurrentSentenceId: (currentSentenceId) => {
        set(() => ({ currentSentenceId: currentSentenceId }));
      },
      setTimeToLearnOne: (time) => {
        set(() => ({ timeToLearnOneConversation: time }));
      },
      setConversations: (conversations: Array<Array<SentenceEntity>>) => {
        set(() => ({ conversations }));
      },
      setSpeakings: (speakings: Array<Array<SentenceEntity>>) => {
        set(() => ({ speakings }));
      },
      setSelectedSentence: (selectedSentence: SentenceEntity) => {
        set(() => ({ selectedSentence: selectedSentence }));
      },
      setConversationsByParagraph: (conversationsByParagraph: Array<Array<SentenceEntity>>) => {
        set(() => ({ conversationsByParagraph }));
      },
      setParagraphs: (paragraphs: Array<ParagraphEntity>) => {
        set(() => ({ paragraphs }));
      },
      setLearnAgain: (isLearnAgain: boolean) => {
        set(() => ({ isLearnAgain }));
      },
      setStartLearn: (isStartLearn: boolean) => {
        set(() => ({ isStartLearn }));
      },
      setFinishLearn: (isFinishLearn: boolean) => {
        set(() => ({ isFinishLearn }));
      },
      setActiveTab: (activeTab: LearnTypeEnum) => {
        set(() => ({ activeTab }));
      },
      setSentenceGroupsId: (sentenceGroupsId: number) => {
        set(() => ({ sentenceGroupsId }));
      },
    }),
    {
      name: APP_STORE_NAME,
      storage: createJSONStorage(() => sessionStorage),
      partialize: (state) =>
        Object.fromEntries(Object.entries(state).filter(([key]) => ['showWord'].includes(key))),
    }
  )
);

export default useLearnStore;
