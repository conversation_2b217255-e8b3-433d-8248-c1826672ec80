'use client';

import React, { ReactNode } from 'react';

import useSidebarStore from '@/store/sidebar';
import classNames from 'classnames';

type AppHeaderProps = {
  children: ReactNode;
  className?: string;
  bottom?: string;
};
const AppHeader = ({ children, className = '', bottom = '0px' }: AppHeaderProps) => {
  const { isOpenSidebar, setOpenSidebar } = useSidebarStore();
  return (
    <>
      <div className={'flex items-center'}>
        <div>
          <button
            className="ml-[30px] top-4 cursor-pointer text-black opacity-50 lg:hidden py-1 text-xl leading-none bg-transparent rounded border border-solid border-transparent"
            type="button"
            onClick={() => setOpenSidebar(!isOpenSidebar)}
          >
            <i className="text-2xl icon-menu text-color-minor" />
          </button>
        </div>
        <div
          className={classNames(`items-center flex w-full duration-1000 ${className} font-medium`)}
        >
          {children}
        </div>
      </div>

      <div
        className={'border-b border-bg-box h-0 w-full'}
        style={{
          borderBottomWidth: bottom,
        }}
      ></div>
    </>
  );
};
export default AppHeader;
