'use client';

import { useCallback, useEffect, useState } from 'react';

import { AccessEnum } from '@/configs/CanAccess';
import LearnTypeEnum from '@/configs/LearnTypeEnum';
import { StatusEnum } from '@/configs/StatusEnum';
import { QUERY_KEY } from '@/constant/query-key';
import { useConversationContext } from '@/providers/ConversationProvider';
import { useAppContext } from '@/store/contexts/AppContext';
import useLearnStore from '@/store/learn';
import { AppContextProps } from '@/types/theme';
import checkAccess from '@/utils/checkAccess';
import { useQueryClient } from '@tanstack/react-query';
import classNames from 'classnames';
import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';

import Button from '@/components/Button';
import Tabs from '@/components/Tabs';
import { Tooltip } from '@/components/Tooltip';

import { useApproveConversationMutation } from '@/hooks/Ent/useConversations';
import { useSession } from '@/hooks/useSession';

import ModalConfirmExitPage from './ModalConfirmExitPage';
import TabShowWord from './TabShowWord';
import Translations from './Translation/Translations';
import { Exercise } from './exercise';
import { LessonContainer } from './lesson/LessonContainer';
import { ListenContainer } from './listen/ListenContainer';
import SentenceSkeleton from './skeleton/SentenceSkeleton';
import SpeakingContainer from './speaking/SpeakingContainer';

const LearnContainer = () => {
  const { data: session } = useSession();
  const t = useTranslations();
  const queryClient = useQueryClient();
  const [exerciseProgress, setExerciseProgress] = useState(0);
  const {
    paragraph,
    isLoading,
    activeTab,
    tabs,
    setActiveTab,
    isConfirmChangeExitPage,
    setIsConfirmChangeExitPage,
  } = useConversationContext();
  const { selectedSentence } = useLearnStore();

  const approveConversationMutation = useApproveConversationMutation();

  const handleApproveConversation = async () => {
    try {
      if (!paragraph) return;
      const response = await approveConversationMutation.mutateAsync({
        paragraph_id: paragraph.id,
        process_approve: StatusEnum.ON,
      });
      if (response.status === 200) {
        await queryClient.invalidateQueries({ queryKey: [QUERY_KEY.PARAGRAPH] });
        toast.success(t('learn.approve_success'));
      } else {
        toast.error(t('learn.approve_error'));
      }
    } catch (error) {
      toast.error(t('learn.approve_error'));
      console.log(error);
    }
  };
  const { setPanel, setOpenPanel }: AppContextProps = useAppContext();

  const TranslationCallBack = useCallback(() => {
    return <Translations />;
  }, [paragraph]);

  useEffect(() => {
    if (selectedSentence && paragraph?.process_approve === StatusEnum.ON) {
      // @ts-ignore
      setPanel(TranslationCallBack);
      setOpenPanel(true);
    }
  }, [selectedSentence]);

  if (paragraph?.paragraph_current_id === 0 && !isLoading)
    return (
      <div className={'flex items-center h-72 justify-center'}>
        <span dangerouslySetInnerHTML={{ __html: t('learn.no_conversation') }} />
      </div>
    );
  // if (isExpired) return <LearnError message={message} />;

  return (
    <div className={'w-full h-full flex-1 flex flex-col overflow-visible'}>
      <ModalConfirmExitPage
        activeTab={activeTab}
        openModalConfirmQuit={isConfirmChangeExitPage}
        setOpenModalConfirmQuit={setIsConfirmChangeExitPage}
        onConfirm={() => {
          setIsConfirmChangeExitPage(false);
        }}
      />

      <div
        className={classNames(
          'flex flex-col justify-center w-full bg-bg-general pl-[30px] pr-5 relative z-20',
          activeTab !== LearnTypeEnum.EXERCISE && 'border-b-bg-box border-b-[10px]'
        )}
      >
        {activeTab === LearnTypeEnum.EXERCISE && (
          <div className="h-2.5 absolute top-9 left-0 w-full bg-bg-box">
            <div className="w-full h-full relative">
              <motion.div
                className="h-full bg-primary transition-all duration-300 opacity-50"
                style={{ width: `${exerciseProgress * 100}%` }}
                initial={{ x: 0 }}
                animate={{ x: 0 }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </div>
        )}
        <div
          className={`flex items-start ${paragraph?.document_id ? 'pt-0' : 'pt-2.5'
            } pb-2.5 justify-between w-full`}
        >
          <div
            className={
              'flex items-center gap-x-3 h-[24px] leading-[24px] text-[14px] text-color-major font-medium font-[Inter] overflow-hidden text-ellipsis hover:text-clip'
            }
          >
            <div className={'leading-none whitespace-nowrap flex gap-x-2 items-center'}>
              <i className={'icon-conversation text-base inline'}></i>
              {paragraph?.title || ''}
            </div>
            {paragraph?.process_approve === StatusEnum.ON && (
              <Tooltip content={t('learn.approved')}>
                <div className="rounded-full nline-block w-3 h-3 bg-white overflow-hidden">
                  <i className={'text-base icon-ok-circled text-primary-100 w-full h-full'} />
                </div>
              </Tooltip>
            )}
          </div>
          {!isLoading && (
            <div className={'flex items-center justify-end gap-[18px]'}>
              {!isLoading && (
                <Tabs
                  tabs={tabs}
                  activeTabId={activeTab}
                  onSelectionChange={(tab) => setActiveTab(tab as LearnTypeEnum)}
                />
              )}
              {![LearnTypeEnum.APPROVE, LearnTypeEnum.EXERCISE].includes(activeTab) ? (
                <TabShowWord />
              ) : null}
              {checkAccess(session, AccessEnum.LEARN_APPROVE) &&
                paragraph?.process_approve === StatusEnum.OFF && (
                  <Button
                    size="sm"
                    variant={'bordered'}
                    as={'a'}
                    className="h-[25px]"
                    disabled={approveConversationMutation.isPending}
                    onClick={() => {
                      handleApproveConversation();
                    }}
                  >
                    {approveConversationMutation.isPending
                      ? t('learn.btn_approve_waiting')
                      : t('learn.btn_approve')}
                  </Button>
                )}
            </div>
          )}
        </div>
      </div>
      <div className={classNames('flex-1 flex flex-col overflow-visible h-full')}>
        <div className={classNames('p-0 flex-1 w-full flex flex-col items-center justify-center')}>
          <div className={'transition-all duration-2000 relative w-full'}>
            {approveConversationMutation.isPending && <SentenceSkeleton />}
            {activeTab === LearnTypeEnum.LISTEN && <ListenContainer />}
            {activeTab === LearnTypeEnum.SPEAKING && <SpeakingContainer />}
            {activeTab === LearnTypeEnum.APPROVE &&
              checkAccess(session, AccessEnum.LEARN_APPROVE) && <LessonContainer />}
            {activeTab === LearnTypeEnum.EXERCISE && paragraph && (
              <Exercise paragraph={paragraph} setExerciseProgress={setExerciseProgress} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
export default LearnContainer;
