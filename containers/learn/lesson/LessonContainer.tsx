'use client';

import { ConversationTypeEnum } from '@/configs/ConversationEnum';
import { useConversationContext } from '@/providers/ConversationProvider';

import { ConversationLesson } from './ConversationLesson';
import { EssayLesson } from './EssayLesson';
import { GalleryLesson } from './GalleryLesson';

export const LessonContainer = () => {
  const { paragraph } = useConversationContext();

  if (!paragraph) return null;

  return (
    <div
      className={`mb-1 justify-between flex flex-col h-full w-full ${paragraph?.item !== ConversationTypeEnum.GALLERY ? 'pl-8' : ''}`}
    >
      {paragraph.item === ConversationTypeEnum.ESSAY && <EssayLesson />}
      {paragraph.item === ConversationTypeEnum.GALLERY && <GalleryLesson />}
      {paragraph.item === ConversationTypeEnum.CONVERSATION && <ConversationLesson />}
    </div>
  );
};
