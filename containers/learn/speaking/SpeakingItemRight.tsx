'use client';
import { SentenceEntity } from 'types/model';
import { SpeakingItemProps, SpeakingItemRightProps } from 'types/component';
import classNames from 'classnames';
import { map } from 'lodash';
import React, { useEffect, useState } from 'react';
import Avatar from 'components/Avatar';
import useLearnStore from 'store/learn';
import useSpeakingStore from 'store/speaking';
import { SentenceProcessEnum } from 'configs/SentenceProcessEnum';
import { RecordProcessEnum } from '@/configs/RecordProcessEnum';

const SpeakingItem = ({
  speaking,
  position,
  sentenceLength,
  className,
  isLastRecord,
}: SpeakingItemProps) => {
  const { showWord, setSelectedSentence } = useLearnStore();
  const [scoreColor, setScoreColor] = useState<string>('');
  const { setSentence, sentenceScore } = useSpeakingStore();

  useEffect(() => {
    if (isLastRecord) {
      setSentence(speaking);
    } else {
      console.log('not lasted');
      setScoreColor(speaking.color || '');
    }
  }, []);

  useEffect(() => {
    if (sentenceScore && sentenceScore.sentence_id === speaking.id) {
      setScoreColor(sentenceScore.color);
    }
  }, [sentenceScore]);

  const handleSpeakItem = () => {
    setSelectedSentence(speaking);
  };

  return (
    <div className={'flex items-center group relative pl-8 mb-1 max-w-[410px] justify-end'}>
      <div className="flex flex-col space-y-1 text-[0.9375rem] mx-2 order-0 items-end">
        <div className={'flex items-center group col-span-6 justify-end'}>
          <div
            onClick={() => setSelectedSentence(speaking)}
            className={classNames(
              'px-4 order-1 pl-6 cursor-pointer py-1 relative rounded-[23px] inline-block shadow-small text-color-major bg-bg-box',
              {
                'rounded-tr-md': position > 0 && sentenceLength > 1,
                'rounded-br-md': position < sentenceLength - 1,
              },
            )}
          >
            {scoreColor !== '' && (
              <div className={'opacity-100 absolute -left-1.5 top-1/2 -translate-y-1/2  w-4 h-4'}>
                <i
                  className={classNames('text-base icon-ok-circled', {
                    'text-red': scoreColor === 'red',
                    'text-primary': scoreColor === 'green',
                    'text-yellow': scoreColor === 'yellow',
                  })}
                />
              </div>
            )}

            <span
              className={classNames(
                className,
                'transition-all ease-in-out duration-[1s] hover:!opacity-100',
                {
                  '!opacity-100': showWord,
                  '!opacity-5': isLastRecord && !showWord,
                },
              )}
            >
              {speaking.content}
            </span>
          </div>
          {!sentenceScore && (
            <div
              className={
                'w-6 h-6 ml-2 items-center cursor-pointer group-hover:flex justify-center rounded-full hover:bg-bg-box hidden absolute left-0'
              }
            >
              <span onClick={handleSpeakItem}>
                <i className={'text-medium text-color-minor icon-mic'} />
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const SpeakingItemRight = ({
  sentences,
  activeCharacter,
  isLastItem,
  className,
  isNextAction,
  isBackAction,
}: SpeakingItemRightProps) => {
  const [rightSentences, setRightSentences] = useState<Array<SentenceEntity>>([]);
  const { recordProcess, setSentenceProcess, sentence, setSentence, setRecordProcess } = useSpeakingStore();
  const { currentSentenceId } = useLearnStore();

  useEffect(() => {
    console.log('recordProcess', recordProcess, isBackAction);

    if (!isLastItem) {
      setRightSentences([...sentences]);
    } else {
      if (recordProcess === RecordProcessEnum.INIT) {
        handleListSentence();
      }
    }
  }, [recordProcess]);

  useEffect(() => {
    if (isBackAction && isLastItem) {
      const currentSentenceIndex = sentences.findIndex((item) => item.id === sentence?.id);
      console.log('currentSentenceIndex', currentSentenceIndex, sentence);
      if (currentSentenceIndex >= 0) {
        setRightSentences(sentences.slice(0, currentSentenceIndex + 1));
      }
    }
  }, [isBackAction, sentence]);

  const handleListSentence = () => {
    const lengthOfRightSentences = rightSentences.length || 0;
    if (lengthOfRightSentences >= sentences.length && isNextAction) {
      console.log('lengthOfRightSentences >= sentences.length');
      setSentenceProcess(SentenceProcessEnum.FINISH);
      return;
    }

    let foundCurrentSentence = false;
    const existCurrentSentence = sentences.some((sentence) => sentence.id === currentSentenceId);
    if (existCurrentSentence && !isNextAction) {
      console.log('existCurrentSentence && !isNextAction', sentence);
      setRightSentences([]);
      for (const sentenceItem of sentences) {
        setSentence(sentenceItem);
        setRightSentences((prevSentences) => [...prevSentences, sentenceItem]);
        if (sentenceItem.id === currentSentenceId) {
          foundCurrentSentence = true;
          setSentenceProcess(SentenceProcessEnum.PROCESS);
          break;
        }
      }
      if (!foundCurrentSentence) {
        console.log('not found current sentence');
        setSentenceProcess(SentenceProcessEnum.FINISH);
      }
    } else {
      console.log('existCurrentSentence && isNextAction');
      const nextSentence = sentences[lengthOfRightSentences] || null;
      console.log('right sentence ', nextSentence, sentences, lengthOfRightSentences);
      if (nextSentence) {
        console.log(4444444);
        setSentence(nextSentence);
        setRightSentences((prevState) => [...prevState, nextSentence]);
        setRecordProcess(RecordProcessEnum.PROCESS);
      }
    }
  };

  return (
    <div className={'flex items-end justify-end mb-4'}>
      <div
        className={'flex flex-col space-y-1 text-[0.9375rem] mx-2 order-1 items-end justify-end'}
      >
        <div className="text-right px-4 text-[0.75rem] text-color-minor ">
          {activeCharacter?.fullname || ''}
        </div>
        {rightSentences &&
          map(rightSentences, (speaking, index, array) => (
            <SpeakingItem
              key={speaking.id}
              speaking={speaking}
              isLastRecord={speaking.id === array[rightSentences.length - 1].id && isLastItem}
              position={index}
              className={className}
              sentenceLength={rightSentences.length}
            />
          ))}
      </div>
      <Avatar name={rightSentences[0]?.character_id?.toString()} size={32} className={'order-1'} />
    </div>
  );
};
export default SpeakingItemRight;
