'use client';

import Head from 'next/head';

import LearnContainerSkeleton from '@/containers/learn/skeleton/LearnContainerSkeleton';
import { useConversationContext } from '@/providers/ConversationProvider';
import Header<PERSON>earn from 'containers/learn/HeaderLearn';

import LearnContainer from './LearnContainer';

const LearnPageContainer = () => {
  // Determine loading state

  const { paragraph, isLoading } = useConversationContext();

  return (
    <>
      <Head>
        <title>Họ<PERSON> bài {paragraph?.title ? ` - ${paragraph.title}` : ''}</title>
      </Head>
      <div className="flex flex-col min-h-screen">
        {isLoading ? (
          <div className="relative">
            <LearnContainerSkeleton />
          </div>
        ) : (
          <>
            <HeaderLearn />
            <LearnContainer />
          </>
        )}
      </div>
    </>
  );
};

export default LearnPageContainer;
