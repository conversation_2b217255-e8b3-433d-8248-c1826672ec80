'use client';

import React, { useEffect, useState } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/navigation';

import GroupTypeEnum from '@/configs/GroupTypeEnum';
import MemberExerciseSkeleton from '@/containers/group/skeleton/MemberExerciseSkeleton';
import classNames from 'classnames';
import Button from 'components/Button';
import ScrollArea from 'components/ScrollArea';
import EntRouters from 'configs/EntRouters';
import Header from 'containers/class/Header';
import useGroupMember from 'hooks/Ent/useGroupMember';
import { map } from 'lodash';
import InfiniteScroll from 'react-infinite-scroll-component';

import useMemberExercise from '@/hooks/Ent/useMemberExercise';

const MemberExercisesList = ({ groupId, accountExerciseId }) => {
  const router = useRouter();
  const { memberExercisesList, isLoading, isReachingEnd, page, setPage } = useMemberExercise({
    groupId: groupId,
    memberId: accountExerciseId,
  });
  const { group } = useGroupMember(parseInt(groupId));
  const [headerData, setHeaderData] = useState<{ id: number; title: string }[]>([]);

  useEffect(() => {
    if (group) {
      if (group.type !== GroupTypeEnum.CLASS) {
        router.push('/');
        return;
      }
      const newData: { id: number; title: string }[] = [];
      if (group.parent_title) {
        newData.push({ id: group.parent_id, title: group.parent_title });
      }
      newData.push({ id: group.id, title: group.title });
      setHeaderData(newData);
    }
  }, [group]);

  return (
    <>
      <Header key={`header-${groupId}`} token={group?.token} breadcrumbs={headerData} />
      <div className="flex mb-[10px] items-center">
        <Link href={`${EntRouters.class}/${groupId}/exercise`}>
          <Button
            size={'xs'}
            variant={'bordered'}
            color={'default'}
            className={'float-end px-2 ml-[30px]'}
          >
            <i className={classNames('icon-arrow-left text-normal')} /> Quay lại
          </Button>
        </Link>
        <div className={'ml-[10px]'}>{memberExercisesList?.[0]?.paragraph?.title}</div>
        <i className="ml-2.5 text-medium icon-more-fill" />
      </div>

      <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
        <InfiniteScroll
          height={'calc(100vh - 77px)'}
          key="paragraph"
          dataLength={memberExercisesList.length}
          next={() => setPage(page + 1)}
          hasMore={!isReachingEnd}
          loader={null}
          className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
        >
          <table className="table-auto w-full">
            <thead className="sticky top-0 z-[1]">
              <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
                <th className={'pr-2 pl-[30px] py-1 text-left font-normal'}>Bài giao</th>
                <th className={'py-1 text-left font-normal'}>Lần nghe bài</th>
                <th className={'py-1 text-left font-normal pr-[30px]'}>Tra từ</th>
                <th className={'py-1 text-left font-normal pr-[30px]'}>Điểm nói</th>
                {/* <th className={'py-1 text-left font-normal pr-[30px]'}>Nhận xét</th> */}
                {/* <th className={'py-1 text-left font-normal pr-[30px]'}></th> */}
              </tr>
            </thead>
            <tbody className={'text-[0.8123rem]'}>
              {map(memberExercisesList, (item, key) => (
                <tr
                  key={`tr-${key}`}
                  className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group "
                >
                  <td>
                    <Link
                      className="flex pl-[20px] py-[12px]"
                      href={`${EntRouters.group}/${groupId}/member/${accountExerciseId}/${item.id}-${item.token}`}
                      key={key}
                    >
                      <i className={'icon-account text-[16px] mr-[10px]'}></i>
                      {item.member?.fullname}
                    </Link>
                  </td>
                  <td className="">{item.listen}</td>
                  <td className="">{item.lookup || 0}</td>
                  <td>
                    <div className="flex items-center">
                      <i className="icon-ok-circled text-[14px] text-primary-100 mr-[2px]"></i>

                      <div className="mr-[10px]">{item.speak_high || 0}</div>

                      <i className="icon-ok-circled text-[14px] text-yellow mr-[2px]"></i>
                      <div className="mr-[10px]">{item.speak_medium || 0}</div>

                      <i className="icon-cancel text-[14px] text-red mr-[2px]"></i>
                      <div>{item.speak_low || 0}</div>
                    </div>
                  </td>
                  {/* <td>{item.note}</td> */}
                  {/* <td>
                    <i className="ml-2.5 text-medium icon-more-fill" />
                  </td> */}
                </tr>
              ))}
              {isLoading ? <MemberExerciseSkeleton /> : null}
            </tbody>
          </table>
        </InfiniteScroll>
      </ScrollArea>
    </>
  );
};
export default MemberExercisesList;
