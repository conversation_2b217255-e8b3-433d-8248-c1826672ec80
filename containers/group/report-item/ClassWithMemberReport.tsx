'use client';

import React, { useMemo, useRef } from 'react';

import { ReportTypeEnum } from '@/configs/ReportTypeEnum';
import ChartBarSkeleton from '@/containers/class/skeleton/ChartBarSkeleton';
import useReportStore from '@/store/report';
import { ChartItem } from '@/types/component';
import { reduce } from 'lodash';
import { Bar, Line } from 'recharts';

import Charts from '@/components/Charts/Charts';

import useChartConfig from '@/hooks/Ent/useChartConfig';
import useReportMember from '@/hooks/Ent/useReportMember';
import useReportToken from '@/hooks/Ent/useReportToken';

const ClassWithMemberReport = ({ title }) => {
  const { params, loading } = useReportStore();
  const totalStudent = useRef(0);
  const totalClass = useRef(0);
  const { makeBarDate } = useChartConfig();
  const { reportsData, isLoading } = useReportMember(
    {
      ...params,
    },
    loading || params.start_day === 0 || params.end_day === 0
  );

  const { reportsData: reportDataClass } = useReportToken(
    {
      ...params,
      object_id: 0,
      item: ReportTypeEnum.ORGANIZATION,
    },
    loading || params.start_day === 0 || params.end_day === 0
  );

  const [charts, columns] = useMemo(() => {
    totalStudent.current = reduce(reportsData, (count, item) => count + item.amount, 0);

    totalClass.current = reportDataClass?.length;
    const colors = ['BLUE', 'GREEN'];
    const groupedClass = reportDataClass?.reduce((acc, item) => {
      acc[item.day] = (acc[item.day] || 0) + 1;
      return acc;
    }, {});

    const charts: ChartItem[] = makeBarDate(reportsData, colors);
    charts.map((item) => {
      item.GREEN = groupedClass[item.date] ?? 0;
      reportsData?.map((report) => {
        if (item.date === report.day) {
          item.BLUE = report.amount;
          item.isActive = true;
        }
      });
    });
    return [charts, colors];
  }, [reportsData, reportDataClass]);

  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || payload.length === 0 || !payload.some((p) => p.value > 0)) {
      return null;
    }

    const genTransactionName = (id) => {
      const ids = {
        BLUE: 'Số lớp',
        GREEN: 'Số học sinh',
      };
      return ids[id] ?? '#';
    };

    return (
      <div className="bg-bg-box p-3 rounded-md shadow-md">
        <p className="font-bold">Ngày {label}</p>
        {payload.reverse().map((entry, index) => (
          <p
            key={index}
            className="text-xs py-1 flex items-center gap-2"
            style={{ color: entry.color }}
          >
            <i className={'w-3 h-3 inline-block'} style={{ backgroundColor: entry.color }}></i>
            {genTransactionName(entry.name)}: {entry.value}
          </p>
        ))}
      </div>
    );
  };

  return (
    <>
      <div className={'col-span-12 sm:col-span-4 md:col-span-3 2xl:col-span-2'}>
        <div className={'grid grid-cols-3'}>
          <div className={'col-span-3 flex'}>
            <div className="mr-[30px] px-[6px]">
              <i className="text-[32px] icon-diamond text-yellow-100" />
            </div>
            <div>
              <div className={'text-small'}>Số học sinh sử dụng</div>
              <span className={'text-[22px] font-medium'}>
                {totalStudent.current?.toLocaleString()}
              </span>
              <div className={'text-small block mt-4'}>Số lớp</div>
              <span className={'text-[22px] font-medium'}>
                {totalClass.current?.toLocaleString() ?? 0}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className={'col-span-12 sm:col-span-8 md:col-span-9 2xl:col-span-10'}>
        <div className={'w-full flex justify-between items-center'}>
          <span className={'text-small font-medium text-color-major'}>{title}</span>
        </div>
        <div className={'mt-2'}>
          <div className={'w-full h-[350px]'}>
            {isLoading ? (
              <ChartBarSkeleton />
            ) : (
              /*@ts-ignore*/
              <Charts charts={charts} columns={columns} tooltip={CustomTooltip}>
                <Bar barSize={30} dataKey={'BLUE'} fill={'rgba(74, 161, 129, 1)'} />
                <Line type="monotone" dataKey={'GREEN'} stroke="#ff7300" />
              </Charts>
            )}
          </div>
        </div>
      </div>
    </>
  );
};
export default ClassWithMemberReport;
