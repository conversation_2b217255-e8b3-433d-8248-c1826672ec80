import {useEffect, useRef, useReducer, useCallback} from 'react';

import {TIMEOUT_SPEAKING} from 'configs';
import {franc} from 'franc';
import {delay} from 'utils/common';


// Add type declarations for Web Speech API
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }

  interface SpeechRecognitionEvent {
    readonly results: SpeechRecognitionResultList;
  }

  interface SpeechRecognitionResultList {
    readonly length: number;

    item(index: number): SpeechRecognitionResult;

    readonly [index: number]: SpeechRecognitionResult;
  }

  interface SpeechRecognitionResult {
    readonly isFinal: boolean;

    readonly [index: number]: SpeechRecognitionAlternative;
  }

  interface SpeechRecognitionAlternative {
    readonly transcript: string;
    readonly confidence: number;
  }
}

const VOLUME_THRESHOLD = 13; // Ngưỡng âm lượng tối thiểu

// Định nghĩa các action types
const actionTypes = {
  SET_IS_START_MICROPHONE: 'SET_IS_START_MICROPHONE',
  SET_AUDIO_BLOB: 'SET_AUDIO_BLOB',
  SET_IS_SPEAKING: 'SET_IS_SPEAKING',
  SET_AUDIO_CONTEXT: 'SET_AUDIO_CONTEXT',
  SET_ANALYSER: 'SET_ANALYSER',
  SET_STOP_BY_TIMEOUT: 'SET_STOP_BY_TIMEOUT',
  SET_IS_ENGLISH: 'SET_IS_ENGLISH',
  FORCE_UPDATE: 'FORCE_UPDATE',
  SET_RANDOM: 'SET_RANDOM',
  SET_RECORD_TIME: 'SET_RECORD_TIME',
  SET_RECORD_TIME_INTERVAL: 'SET_RECORD_TIME_INTERVAL',
  SET_IS_PAUSED: 'SET_IS_PAUSED',
  SET_SILENT_COUNT: 'SET_SILENT_COUNT',
  SET_TRANSCRIPT: 'SET_TRANSCRIPT',
};

// Định nghĩa state tổng hợp
const initialState = {
  isStartMicrophone: false,
  audioBlob: null,
  isSpeaking: false,
  audioContext: null,
  analyser: null,
  stopByTimeout: false,
  isEnglish: true,
  forceUpdate: 0,
  random: 0,
  recordTime: 0,
  recordTimeInterval: null,
  isPaused: false,
  silentCount: 0,
  transcript: '',
};

// Reducer function
function reducer(state, action) {
  switch (action.type) {
    case actionTypes.SET_IS_START_MICROPHONE:
      return {...state, isStartMicrophone: action.payload};
    case actionTypes.SET_AUDIO_BLOB:
      return {...state, audioBlob: action.payload};
    case actionTypes.SET_IS_SPEAKING:
      return {...state, isSpeaking: action.payload};
    case actionTypes.SET_AUDIO_CONTEXT:
      return {...state, audioContext: action.payload};
    case actionTypes.SET_ANALYSER:
      return {...state, analyser: action.payload};
    case actionTypes.SET_STOP_BY_TIMEOUT:
      return {...state, stopByTimeout: action.payload};
    case actionTypes.SET_IS_ENGLISH:
      return {...state, isEnglish: action.payload};
    case actionTypes.FORCE_UPDATE:
      return {...state, forceUpdate: state.forceUpdate + 1};
    case actionTypes.SET_RANDOM:
      return {...state, random: Math.random()};
    case actionTypes.SET_RECORD_TIME:
      return {...state, recordTime: action.payload};
    case actionTypes.SET_RECORD_TIME_INTERVAL:
      return {...state, recordTimeInterval: action.payload};
    case actionTypes.SET_IS_PAUSED:
      return {...state, isPaused: action.payload};
    case actionTypes.SET_SILENT_COUNT:
      return {...state, silentCount: action.payload};
    case actionTypes.SET_TRANSCRIPT:
      return {...state, transcript: action.payload};
    default:
      return state;
  }
}

const useMicrophone = () => {
  const userMediaStreamRef = useRef<MediaStream | null>(null);
  const mediaRecorder = useRef<MediaRecorder | null>(null);
  const stopBeepAudioRef = useRef<HTMLAudioElement | null>(null);
  const startBeepAudioRef = useRef<HTMLAudioElement | null>(null);
  const isCancelRef = useRef<boolean>(false);
  const [state, dispatch] = useReducer(reducer, initialState);
  const recognitionRef = useRef<any>(null);
  const transcriptRef = useRef<string>(''); // Ref để lưu transcript mới nhất

  // Helper
  const setIsRecording = (val) => dispatch({type: actionTypes.SET_IS_START_MICROPHONE, payload: val});
  const setAudioBlob = (val) => dispatch({type: actionTypes.SET_AUDIO_BLOB, payload: val});
  const setIsSpeaking = (val) => dispatch({type: actionTypes.SET_IS_SPEAKING, payload: val});
  const setAudioContext = (val) => dispatch({type: actionTypes.SET_AUDIO_CONTEXT, payload: val});
  const setAnalyser = (val) => dispatch({type: actionTypes.SET_ANALYSER, payload: val});
  const setStopByTimeout = (val) => dispatch({type: actionTypes.SET_STOP_BY_TIMEOUT, payload: val});
  const setIsEnglish = (val) => dispatch({type: actionTypes.SET_IS_ENGLISH, payload: val});
  const setRandom = () => dispatch({type: actionTypes.SET_RANDOM});
  const setRecordTime = (val) => dispatch({type: actionTypes.SET_RECORD_TIME, payload: val});
  const setRecordTimeInterval = (val) => dispatch({type: actionTypes.SET_RECORD_TIME_INTERVAL, payload: val});
  const setIsPaused = (val) => dispatch({type: actionTypes.SET_IS_PAUSED, payload: val});
  const setSilentCount = (val) => dispatch({type: actionTypes.SET_SILENT_COUNT, payload: val});
  const setTranscript = useCallback((val) => {
    transcriptRef.current = val; // Cập nhật ref ngay lập tức
    dispatch({type: actionTypes.SET_TRANSCRIPT, payload: val});
  }, []);

  const startMicrophone = async (step = 0) => {
    console.log('step', step);
    console.log('start recording ...', Date.now());

    setStopByTimeout(false);
    if (userMediaStreamRef.current) {
      userMediaStreamRef.current.getAudioTracks().forEach((track) => {
        track.enabled = false;
      });
    }
    if (startBeepAudioRef.current) {
      await startBeepAudioRef.current.play();
    }
    if (userMediaStreamRef.current) {
      userMediaStreamRef.current.getAudioTracks().forEach((track) => {
        track.enabled = true;
      });
    }
    setAudioBlob(null); // Reset audioBlob at the start of recording
    setSilentCount(0); // Reset silentCount
    setRecordTime(0); // Reset record time
    setIsPaused(false); // Reset isPaused
    setIsEnglish(true); // Reset language detection
    setTranscript(''); // Reset transcript
    transcriptRef.current = ''; // Reset transcript ref

    // Initialize speech recognition
    recognitionRef.current = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
    recognitionRef.current.continuous = true;
    recognitionRef.current.lang = 'en-US';
    recognitionRef.current.interimResults = true;

    recognitionRef.current.onresult = (event: SpeechRecognitionEvent) => {
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = 0; i < event.results.length; i++) {
        const result = event.results[i];
        const transcript = result[0].transcript;

        if (result.isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      // Cập nhật transcript với kết quả final hoặc interim
      const currentTranscript = finalTranscript || interimTranscript;
      if (currentTranscript.trim()) {
        setTranscript(currentTranscript.trim());
      }
    };

    recognitionRef.current.onerror = (event: any) => {
      console.error('Speech recognition error:', event.error);
    };

    recognitionRef.current.start();
    setRandom();
    await initMicrophone();
    const startTime = performance.now();
    let previousTime = startTime;
    const updateRecordTime = () => {
      const currentTime = performance.now();
      if (!state.isPaused) {
        setRecordTime(state.recordTime + (currentTime - previousTime) / 1000);
      }
      previousTime = currentTime;
      setRecordTime(Math.floor((currentTime - startTime) / 1000));
      setRecordTimeInterval(requestAnimationFrame(updateRecordTime));
    };
    if (!state.recordTimeInterval) {
      setRecordTimeInterval(requestAnimationFrame(updateRecordTime));
    }
  };

  const stopMicrophone = async (isStopByTimeOut = false) => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    if (isStopByTimeOut) setStopByTimeout(true);
    if (mediaRecorder.current) mediaRecorder.current.stop();
    setIsPaused(false); // Reset isPaused
    if (typeof state.recordTimeInterval === 'number') {
      cancelAnimationFrame(state.recordTimeInterval);
    } // Cancel the recordTime animation frame
    setRecordTimeInterval(null); // Reset the interval reference
    setIsRecording(false);
    releaseMicrophone();
    console.log('stop!', Date.now());
    setRandom();
    if (stopBeepAudioRef.current) {
      await stopBeepAudioRef.current.play();
    }
    await delay(100); // Adjust the delay as needed
  };

  const checkLanguage = useCallback(async (): Promise<boolean> => {
    try {
      // Sử dụng ref để lấy transcript mới nhất
      const text = transcriptRef.current || state.transcript;
      console.log('checkLanguage - text from ref:', transcriptRef.current);
      console.log('checkLanguage - text from state:', state.transcript);
      console.log('checkLanguage - final text used:', text);

      if (!text || text.trim() === '') {
        console.log('No transcript available, defaulting to English');
        return true;
      }

      // Use franc to detect language
      const detectedLang = franc(text, {minLength: 3});
      const isEnglish = detectedLang !== 'vi' && detectedLang !== 'vie';
      console.log('Language detection result:', {
        text,
        detectedLang,
        isEnglish
      });
      return isEnglish;
    } catch (error) {
      console.error('Language detection error:', error);
      return true; // Default to English on error
    }
  }, [state.transcript]);

  const initMicrophone = async () => {
    try {
      userMediaStreamRef.current = await navigator.mediaDevices.getUserMedia({audio: true});

      if (userMediaStreamRef.current) {
        // @ts-ignore
        mediaRecorder.current = new MediaRecorder(userMediaStreamRef.current);

        let chunks: Blob[] = [];

        mediaRecorder.current.ondataavailable = (event) => {
          if (event.data.size > 0) {
            chunks.push(event.data);
          }
        };

        mediaRecorder.current.onstop = async () => {
          if (isCancelRef.current) {
            isCancelRef.current = false;
            chunks = [];
            return;
          }
          const blob = new Blob(chunks, {type: 'audio/wav'});

          if (state.stopByTimeout) {
            console.log('stopByTimeout', state.stopByTimeout);
          }

          // Tăng thời gian delay để đảm bảo transcript được cập nhật
          console.log('Waiting for transcript to be updated...');
          await delay(500); // Tăng từ 100ms lên 500ms

          console.log('Before language check - transcript from ref:', transcriptRef.current);
          console.log('Before language check - transcript from state:', state.transcript);

          // Check language before setting audioBlob
          const isEnglishSpeech = await checkLanguage();
          console.log('Language check result:', isEnglishSpeech);
          setIsEnglish(isEnglishSpeech);

          // Convert and set audio blob only if it's English
          if (isEnglishSpeech) {
            convertSampleRate(blob, 16000).then((convertedBlob) => setAudioBlob(convertedBlob));
          } else {
            setAudioBlob(blob);
          }

          chunks = []; // Clear the chunks for the next recording
        };

        mediaRecorder.current.start();
        setIsRecording(true);
      }
    } catch (err) {
      console.error('Error accessing the microphone:', err);
    }
  };

  const releaseMicrophone = () => {
    if (userMediaStreamRef.current) {
      userMediaStreamRef.current.getTracks().forEach((track) => track.stop());
      userMediaStreamRef.current = null;
    }
    if (state.audioContext && state.audioContext.state !== 'closed') {
      state.audioContext.close();
      setAudioContext(null);
    }
    setAnalyser(null);
  };
  const convertSampleRate = async (audioBlob: Blob, targetSampleRate: number) => {
    const arrayBuffer = await audioBlob.arrayBuffer();
    const audioCtx = new AudioContext();
    const audioBuffer = await audioCtx.decodeAudioData(arrayBuffer);

    const offlineCtx = new OfflineAudioContext(
      audioBuffer.numberOfChannels,
      audioBuffer.duration * targetSampleRate,
      targetSampleRate
    );

    const bufferSource = offlineCtx.createBufferSource();
    bufferSource.buffer = audioBuffer;

    bufferSource.connect(offlineCtx.destination);
    bufferSource.start();
    const renderedBuffer = await offlineCtx.startRendering();
    return audioBufferToWav(renderedBuffer);
  };

  const audioBufferToWav = (buffer: AudioBuffer) => {
    const numOfChan = buffer.numberOfChannels,
      length = buffer.length * numOfChan * 2 + 44,
      bufferArray = new ArrayBuffer(length),
      view = new DataView(bufferArray),
      channels = [];
    let offset = 0;
    let sample = 0;
    let pos = 0;

    setUint32(0x46464952); // "RIFF"
    setUint32(length - 8); // file length - 8
    setUint32(0x45564157); // "WAVE"

    setUint32(0x20746d66); // "fmt " chunk
    setUint32(16); // length = 16
    setUint16(1); // PCM (uncompressed)
    setUint16(numOfChan);
    setUint32(buffer.sampleRate);
    setUint32(buffer.sampleRate * 2 * numOfChan); // avg. bytes/sec
    setUint16(numOfChan * 2); // block-align
    setUint16(16); // 16-bit (hardcoded in this demo)

    setUint32(0x61746164); // "data" - chunk
    setUint32(length - pos - 4); // chunk length

    for (let i = 0; i < buffer.numberOfChannels; i++)
      // @ts-ignore
      channels.push(buffer.getChannelData(i));

    while (pos < length) {
      for (let i = 0; i < numOfChan; i++) {
        sample = Math.max(-1, Math.min(1, channels[i][offset])); // clamp
        sample = (0.5 + sample < 0 ? sample * 32768 : sample * 32767) | 0; // scale to 16-bit signed int
        view.setInt16(pos, sample, true); // write 16-bit sample
        pos += 2;
      }
      offset++; // next source sample
    }

    return new Blob([bufferArray], {type: 'audio/wav'});

    function setUint16(data) {
      view.setUint16(pos, data, true);
      pos += 2;
    }

    function setUint32(data) {
      view.setUint32(pos, data, true);
      pos += 4;
    }
  };

  useEffect(() => {
    // Create audio element for beep sound
    const startAudioElement = new Audio('https://file.langenter.com/config/sound/start.mp3');
    startAudioElement.preload = 'auto';
    startAudioElement.volume = 1; // Adjust volume as needed
    startBeepAudioRef.current = startAudioElement;
    // Create audio element for beep sound
    const stopAudioElement = new Audio('https://file.langenter.com/config/sound/stop.mp3');
    stopAudioElement.preload = 'auto';
    stopAudioElement.volume = 1; // Adjust volume as needed
    stopBeepAudioRef.current = stopAudioElement;
  }, []);

  useEffect(() => {
    const createAudioNodes = () => {
      // @ts-ignore
      const audioCtx = new (window.AudioContext || window.webkitAudioContext)();
      const analyserNode = audioCtx.createAnalyser();
      analyserNode.fftSize = 512;
      // @ts-ignore
      const mediaStreamSource = audioCtx.createMediaStreamSource(userMediaStreamRef.current);
      mediaStreamSource.connect(analyserNode);

      setAudioContext(audioCtx);
      setAnalyser(analyserNode);
    };

    if (userMediaStreamRef.current && !state.audioContext) {
      createAudioNodes();
    }
  }, [userMediaStreamRef.current, state.audioContext]); // Depend on userMediaStreamRef.current

  useEffect(() => {
    if (state.isStartMicrophone && state.audioContext && state.analyser) {
      const checkSpeakingInterval = setInterval(() => {
        checkSpeaking();
      }, 100); // Adjust the interval as needed

      return () => {
        clearInterval(checkSpeakingInterval);
      };
    }
  }, [state.isStartMicrophone, state.audioContext, state.analyser]);
  const checkSpeaking = () => {
    if (state.isPaused) return;
    const volumeThreshold = VOLUME_THRESHOLD; // Set the volume threshold to 100
    const bufferLength = state.analyser?.frequencyBinCount || 0;
    const dataArray = new Uint8Array(bufferLength);

    state.analyser?.getByteFrequencyData(dataArray);
    const average = dataArray.reduce((acc, val) => acc + val, 0) / bufferLength;

    const speaking = average > volumeThreshold;
    //console.log('Average Volume:', average);
    setIsSpeaking(speaking);

    // If volume is below the threshold, increment silentCount; otherwise, reset it
    if (!speaking) {
      setSilentCount(state.silentCount + 1);
    } else {
      setSilentCount(0);
    }
    // If silentCount reaches 30 (3 seconds with a 100ms interval), stop recording
    if (state.silentCount >= TIMEOUT_SPEAKING * 10) {
      stopMicrophone(true);
      if (state.recordTime <= TIMEOUT_SPEAKING) setRecordTime(TIMEOUT_SPEAKING);
      setSilentCount(0); // Reset silentCount after stopping recording
    }
  };

  const pauseMicrophone = async () => {
    if (mediaRecorder.current && mediaRecorder.current.state === 'recording') {
      mediaRecorder.current.pause();
      setIsPaused(true); // Update the ref
      console.log('Paused recording at', Date.now());
      await new Promise((resolve) => setTimeout(resolve, 100));
      return true;
    }
    return false;
  };

  const resumeMicrophone = async () => {
    if (mediaRecorder.current && mediaRecorder.current.state === 'paused') {
      mediaRecorder.current.resume();
      setIsPaused(false); // Update the ref
      await new Promise((resolve) => setTimeout(resolve, 100));
      console.log('Resumed recording at', Date.now());
      return true;
    }
    return false;
  };
  const cancelRecording = () => {
    if (mediaRecorder.current && mediaRecorder.current.state !== 'inactive') {
      console.log('start clear');
      isCancelRef.current = true;
      releaseMicrophone();
      mediaRecorder.current.stop();
    }
    setAudioBlob(null);
    setIsRecording(false);
    setIsPaused(false);
    setRandom();
    delay(100);
  };
  useEffect(() => {
    cancelRecording();
    console.log('init record');
    // Cleanup khi component unmounts
    return () => {
      console.log('checkout record');
      cancelRecording();
    };
  }, []);

  const downloadAudio = () => {
    if (state.audioBlob) {
      const url = URL.createObjectURL(state.audioBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'recording.wav'; // Tên file với đuôi .wav
      document.body.appendChild(a);
      a.click();
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 0);
    }
  };

  // Function để lấy transcript mới nhất
  const getCurrentTranscript = useCallback(() => {
    return transcriptRef.current || state.transcript;
  }, [state.transcript]);

  return {
    ...state,
    startMicrophone,
    stopMicrophone,
    pauseMicrophone,
    resumeMicrophone,
    cancelRecording,
    downloadAudio,
    setAudioBlob,
    getCurrentTranscript, // Export function để lấy transcript mới nhất
  };
};

export default useMicrophone;
