import { useCallback, useEffect, useReducer, useRef } from 'react';
import { flatten, some } from 'lodash';
import { delay } from '@/utils/common';
import useLearnStore from '@/store/learn';
import useSpeakingStore from '@/store/speaking';
import useBalanceStore from '@/store/balance';
import useMicrophone from '@/hooks/Ent/useMicrophone';
import { useSession } from '@/hooks/useSession';
import { useBackSentenceMutation, useSendSpeakMutation } from '@/hooks/Ent/useSentence';
import { SentenceEntity, Character } from '@/types/model';
import { ErrorCode, TIMEOUT_SPEAKING } from '@/configs';
import { StatusEnum } from '@/configs/StatusEnum';
import { RecordProcessEnum } from '@/configs/RecordProcessEnum';
import { SentenceProcessEnum } from '@/configs/SentenceProcessEnum';
import BackActionEnum from '@/configs/ConversationEnum';
import toast from 'react-hot-toast';
import { useTranslations } from 'next-intl';
import hotkeys from 'hotkeys-js';
import LearnTypeEnum from '@/configs/LearnTypeEnum';

// Define state interface
interface SpeakingState {
    isSendingRecord: boolean;
    isCountDown: boolean;
    isPauseSpeak: boolean;
    isNextAction: boolean;
    isBackAction: boolean;
    showTooltipRecord: boolean;
    isStartLearn: boolean;
}

// Define action types
type SpeakingAction =
    | { type: 'SET_SENDING_RECORD'; payload: boolean }
    | { type: 'SET_SHOW_COUNT_DOWN'; payload: boolean }
    | { type: 'SET_PAUSE_SPEAK'; payload: boolean }
    | { type: 'SET_NEXT_ACTION'; payload: boolean }
    | { type: 'SET_BACK_ACTION'; payload: boolean }
    | { type: 'SET_SHOW_TOOLTIP_RECORD'; payload: boolean }
    | { type: 'SET_START_LEARN'; payload: boolean }
    | { type: 'RESET_STATE' }

// Initial state
const initialState: SpeakingState = {
    isSendingRecord: false,
    isCountDown: true,
    isPauseSpeak: false,
    isNextAction: true,
    isBackAction: false,
    showTooltipRecord: false,
    isStartLearn: true,
};

// Reducer function
function speakingReducer(state: SpeakingState, action: SpeakingAction): SpeakingState {
    switch (action.type) {
        case 'SET_SENDING_RECORD':
            return { ...state, isSendingRecord: action.payload };
        case 'SET_SHOW_COUNT_DOWN':
            return { ...state, isCountDown: action.payload };
        case 'SET_PAUSE_SPEAK':
            return { ...state, isPauseSpeak: action.payload };
        case 'SET_NEXT_ACTION':
            return { ...state, isNextAction: action.payload };
        case 'SET_BACK_ACTION':
            return { ...state, isBackAction: action.payload };
        case 'SET_SHOW_TOOLTIP_RECORD':
            return { ...state, showTooltipRecord: action.payload };
        case 'SET_START_LEARN':
            return { ...state, isStartLearn: action.payload };
        case 'RESET_STATE':
            return { ...initialState, isCountDown: false };
        default:
            return state;
    }
}

export function useSpeakingManager({
    conversations,
    characters,
}: {
    conversations: Array<Array<SentenceEntity>>;
    characters: Character[];
}) {
    // Store hooks
    const {
        speakings,
        isFinishLearn,
        activeCharacter,
        exerciseToken,
        setSpeakings,
        setLearnAgain,
        setStartLearn,
        setFinishLearn,
        setCurrentSentenceId,
        setCharacter
    } = useLearnStore();

    const {
        isPaused: isPauseRecord,
        transcript,
        audioBlob,
        recordTime,
        isStartMicrophone,
        isEnglish,
        setAudioBlob,
        stopMicrophone,
        startMicrophone,
        pauseMicrophone,
        cancelRecording,
        resumeMicrophone
    } = useMicrophone();

    const { balanceStatus } = useBalanceStore();

    const {
        sentence,
        sentenceProcess,
        recordProcess,
        setSentence,
        setSentenceScore,
        setSentenceProcess,
        setRecordProcess,
    } = useSpeakingStore();

    const sendSpeakMutation = useSendSpeakMutation();
    const { data: session } = useSession();
    const t = useTranslations();
    const backSentenceMutation = useBackSentenceMutation();

    // Use reducer instead of multiple useState
    const [state, dispatch] = useReducer(speakingReducer, initialState);
    const { isSendingRecord, isCountDown, isPauseSpeak, isNextAction, isBackAction, showTooltipRecord } = state;

    // Ref for scrolling
    const ref = useRef<HTMLDivElement>(null);

    // Scroll to bottom when speakings change
    useEffect(() => {
        if (speakings.length > 0) {
            ref.current?.scrollIntoView({ behavior: 'smooth' });
        }
    }, [speakings.length]);

    // Reset states when active character changes
    useEffect(() => {
        if (conversations.length === 0) return;
        cancelRecording();
        setSpeakings([]);
        setSentence(null);
        setSentenceProcess(SentenceProcessEnum.PROCESS);
        setRecordProcess(RecordProcessEnum.INIT);
        const currentSentenceId = activeCharacter?.sentence_current_id || 0;
        setCurrentSentenceId(currentSentenceId);
        const _conversations: Array<Array<SentenceEntity>> = [];
        const existCurrentSentence = conversations.some((sentences) =>
            sentences.some((sentence) => sentence.id === currentSentenceId),
        );
        if (currentSentenceId > 0 && existCurrentSentence) {
            some(conversations, (sentences) => {
                _conversations.push(sentences);
                return sentences.some((sentence) => sentence.id === currentSentenceId);
            });
        }
        setSpeakings([..._conversations]);
        setLearnAgain(false);
    }, [activeCharacter?.id, conversations.length]);

    const handleSetBackSentence = (sentence: SentenceEntity) => {
        backSentenceMutation.mutate({
            activeTab: LearnTypeEnum.SPEAKING,
            sentence_id: sentence?.id || 0,
            character_id: sentence?.character_id || 0,
            paragraph_id: sentence?.paragraph_id || 0,
            member_exercise_token: exerciseToken || '',
        });
    };

    // Business logic handlers - Use refs to avoid dependencies
    const handleSendSpeak = useCallback((audioBlob: Blob | null, sentence: SentenceEntity) => {
        if (!audioBlob || !sentence || recordProcess !== RecordProcessEnum.PROCESS) return;
        
        if (balanceStatus !== StatusEnum.ON) {
            toast.error(t('learn.note_not_enough_token'), { duration: 10000 });
            return;
        }

        if (!isEnglish) {
            toast.error(t('learn.not_english_speech'), { duration: 5000 });
            setRecordProcess(RecordProcessEnum.FINISH);
            return;
        }

        if (recordTime < TIMEOUT_SPEAKING && !state.isNextAction) {
            handleTooltipAndAudio(sentence);
            return;
        }
            //Whereas recognition of the inherent dignity
        const blobFile = new File([audioBlob], `record-sentence-id-${sentence.id}`);
        console.log('transcript', transcript);
        const params = {
            template: sentence.content || '',
            file: blobFile,
            member_id: session?.member?.id || 8,
            sentence_id: sentence.id || 0,
            paragraph_id: sentence.paragraph_id || 0,
            character_id: sentence.character_id || 0,
            member_exercise_token: exerciseToken && exerciseToken !== '' ? exerciseToken : undefined,
            access_token: session?.accessToken || '',
            transcript: transcript || '',
        };

        dispatch({ type: 'SET_SENDING_RECORD', payload: true });
        sendSpeakMutation.mutateAsync(params).then(async (res) => {
            setAudioBlob(null);
            console.log(res);
            dispatch({ type: 'SET_SENDING_RECORD', payload: false });

            if (![ErrorCode.NOT_FOUND_BALANCE, ErrorCode.NOT_ENOUGH_BALANCE].includes(res?.message)) {
                await delay(100);
                setRecordProcess(RecordProcessEnum.INIT);
            }

            if (res?.id) {
                setSentenceScore(res);
            }

            const flattenSpeakings = flatten(speakings);
            const isLastSentenceInCurrentConversation = flattenSpeakings[flattenSpeakings.length - 1].id === sentence.id;
            if (isLastSentenceInCurrentConversation) {
                setSentenceProcess(SentenceProcessEnum.FINISH);
            }
            setRecordProcess(RecordProcessEnum.INIT);
            console.log('--------------------------');
        });
    }, [recordProcess, recordTime, balanceStatus, isEnglish, exerciseToken]);

    const handleTooltipAndAudio = useCallback((sentence: SentenceEntity) => {
        dispatch({ type: 'SET_SHOW_TOOLTIP_RECORD', payload: true });

        if (sentence.audios && sentence.audios[0]?.url) {
            const url = sentence.audios[0].url;
            const audio = new Audio('https://file.langenter.com/config/sound/hncs.mp3');
            audio.play();
            audio.onended = () => {
                new Audio(url).play();
                audio.remove();
            };
        }

        setTimeout(() => {
            dispatch({ type: 'SET_SHOW_TOOLTIP_RECORD', payload: false });
        }, 3000);

        console.log('case set FINISH', recordTime > TIMEOUT_SPEAKING, state.isNextAction);
        setRecordProcess(RecordProcessEnum.FINISH);
    }, [recordTime]);

    const handleNextSentence = useCallback(() => {
        if (balanceStatus !== StatusEnum.ON) {
            toast.error(t('learn.note_not_enough_token'), {
                duration: 10000,
            });
            return;
        }

        console.log('handleNextSentence', {
            activeCharacter: activeCharacter?.id,
            isStartLearn: state.isStartLearn,
            isSendingRecord: state.isSendingRecord,
            isCountDown: state.isCountDown,
            sentenceProcess,
            recordProcess,
            isBackAction,
            isNextAction,
            isPauseSpeak
        });

        if (!activeCharacter || state.isStartLearn || state.isSendingRecord || state.isCountDown || sentenceProcess !== SentenceProcessEnum.FINISH) {
            return;
        }

        const conversationLength = conversations.length;
        const speakingLength = speakings.length;
        console.log('conversationLength', conversationLength, speakingLength);
        if (conversationLength === speakingLength && sentenceProcess === SentenceProcessEnum.FINISH) {
            setRecordProcess(RecordProcessEnum.FINISH);
            return;
        }

        if (speakingLength === conversationLength) {
            return;
        }
        dispatch({ type: 'SET_BACK_ACTION', payload: false });
        setSentenceProcess(SentenceProcessEnum.PROCESS);
        const nextConversationItem = conversations[speakings.length];
        console.log('nextConversationItem', nextConversationItem);
        if (nextConversationItem) {
            setSentence(nextConversationItem[0]);
            setSpeakings([...speakings, nextConversationItem]);
        }
    }, [balanceStatus, t, state.isStartLearn, sentenceProcess, activeCharacter, isSendingRecord, isCountDown, speakings, isBackAction]);

    const handeBackSentence = useCallback((action: BackActionEnum = BackActionEnum.PREV) => {
        const currentState = state;

        const isListenAndNotPaused = 
            sentenceProcess === SentenceProcessEnum.PROCESS &&
            !currentState.isPauseSpeak &&
            sentence?.character_id !== activeCharacter?.id && !currentState.isBackAction;
        const isRecordAndNotPaused =
            sentenceProcess === SentenceProcessEnum.PROCESS &&
            !isPauseRecord &&
            sentence?.character_id === activeCharacter?.id && !currentState.isBackAction;

        console.log('isListenAndNotPaused', currentState.isPauseSpeak, sentenceProcess);
        if (isListenAndNotPaused || isRecordAndNotPaused || isSendingRecord) {
            return;
        }
        const listConversation = [...speakings];
        dispatch({ type: 'SET_BACK_ACTION', payload: true });
        dispatch({ type: 'SET_NEXT_ACTION', payload: false });
        if (sentence?.character_id === activeCharacter?.id) {
            cancelRecording();
        } else {
            dispatch({ type: 'SET_PAUSE_SPEAK', payload: false });
        }

        setLearnAgain(true);
    
        delay(100);
        switch (action) {
            case BackActionEnum.PREV:
                if (listConversation.length === 0) break;
                const lastConversation = listConversation[listConversation.length - 1];
                const lastSentence = lastConversation[lastConversation.length - 1];
                console.log('lastSentence', lastSentence);
                if (lastSentence?.character_id !== activeCharacter?.id) {
                    setSentenceProcess(SentenceProcessEnum.FINISH);
                    setRecordProcess(RecordProcessEnum.INIT);
                    dispatch({ type: 'SET_PAUSE_SPEAK', payload: true });
                    listConversation.pop();
                    setSpeakings([...listConversation]);

                    const _lastConversation = listConversation[listConversation.length - 1];
                    if(_lastConversation){
                        const _lastSentence = _lastConversation[_lastConversation.length - 1];
                        setSentence(_lastSentence);
                        handleSetBackSentence(_lastSentence);
                    }else{
                        setSentence(null);
                    }

                    break;
                }
                if (isStartMicrophone) {
                    stopMicrophone();
                }
                console.log('lastConversation', lastConversation);
                const activeSentenceIndex = lastConversation.findIndex(
                    (item) => item.id === sentence?.id,
                );
                const flattenConversation = flatten(listConversation);
                const currentIndex = flattenConversation.findIndex((item) => item.id === sentence?.id);

                const _lastSentence = flattenConversation[currentIndex - 1];
                if (_lastSentence) {
                    setSentence(_lastSentence);
                    
                }
                setRecordProcess(RecordProcessEnum.INIT);
                console.log('activeSentenceIndex', activeSentenceIndex);
                if (activeSentenceIndex > 0) {
                    console.log('activeSentenceIndex > 0', _lastSentence);
                    handleSetBackSentence(_lastSentence);
                    setSentenceProcess(SentenceProcessEnum.START);
                    break;
                }
                console.log('activeSentenceIndex <= 0', _lastSentence);
                setSentenceProcess(SentenceProcessEnum.FINISH);
                listConversation.pop();
                const prevSentences = listConversation[listConversation.length - 1];
                if(prevSentences){
                    const prevSentence = prevSentences[prevSentences.length - 1];
                    setSentence(prevSentence);
                    handleSetBackSentence(prevSentence);
                    if (prevSentence.character_id === activeCharacter?.id) {
                        setSentenceProcess(SentenceProcessEnum.PROCESS);
                    } else {
                        setSentenceProcess(SentenceProcessEnum.FINISH);
                    }
                }else{
                    setSentence(null);
                }
                
                setSpeakings([...listConversation]);

                break;
            case BackActionEnum.START:
                setSentenceProcess(SentenceProcessEnum.FINISH);
                setRecordProcess(RecordProcessEnum.INIT);
                setSpeakings([]);
                setStartLearn(false);
                setLearnAgain(true);
                setFinishLearn(false);
                setCurrentSentenceId(0);
                setSentence(null);
                const firstSentence = conversations[0][0];
                if(firstSentence){
                    handleSetBackSentence(firstSentence);
                }
                break;
            default:
                break;
        }
    }, [sentenceProcess, isPauseRecord, isPauseSpeak, isStartMicrophone, activeCharacter, isSendingRecord, speakings, isBackAction, sentence]);

    const handleStartLearn = () => {
        console.log('handleStartLearn', sentenceProcess);
        setStartLearn(true);
        dispatch({ type: 'SET_NEXT_ACTION', payload: true })
        handleNextSentence();
    };

    const handleFinishCountDown = useCallback((isFinish = false) => {
        dispatch({ type: 'SET_SHOW_COUNT_DOWN', payload: isFinish });
        handleNextSentence();
    }, []);

    const toggleRecording = useCallback(() => {
        if(isSendingRecord) return;
        if (isStartMicrophone) {
            if (!isPauseRecord) {
                pauseMicrophone();
            } else {
                resumeMicrophone();
            }
        } else {
            if (sentence?.character_id === activeCharacter?.id) {
                console.log('effect start microphone');
                startMicrophone();
            }
        }
    }, [isStartMicrophone, isPauseRecord, sentence, activeCharacter, isSendingRecord]);

    // Handle sentence process changes
    useEffect(() => {
        dispatch({ type: 'SET_SHOW_TOOLTIP_RECORD', payload: false });
        console.log('sentenceProcess', sentenceProcess, isSendingRecord, isBackAction);
        if (sentenceProcess !== SentenceProcessEnum.FINISH || isSendingRecord || isBackAction) {
            return;
        }
        dispatch({ type: 'SET_NEXT_ACTION', payload: true });

        const timer = setTimeout(() => {
            handleNextSentence();
        }, 200);

        return () => clearTimeout(timer);
    }, [sentenceProcess, isBackAction, isSendingRecord]);

    // Handle audio blob changes
    useEffect(() => {
        console.log('recordProcess in effect audio blob', recordProcess, audioBlob);
        if (audioBlob && sentence && recordProcess === RecordProcessEnum.PROCESS) {
            handleSendSpeak(audioBlob, sentence);
        }
    }, [audioBlob, sentence, recordProcess]);

    // Handle microphone state changes
    useEffect(() => {
        console.log('Effect triggered with states:', {
            sentenceProcess,
            recordProcess,
            isCountDown,
            isNextAction,
            sentenceId: sentence?.id,
            characterId: sentence?.character_id,
            activeCharacterId: activeCharacter?.id,
            isPauseRecord
        });

        if (
            sentence?.character_id === activeCharacter?.id &&
            isNextAction &&
            recordProcess === RecordProcessEnum.PROCESS &&
            !isCountDown &&
            sentenceProcess !== SentenceProcessEnum.FINISH &&
            !isPauseRecord
        ) {
            startMicrophone();
        }
    }, [recordProcess, sentenceProcess, isCountDown, isNextAction, sentence?.character_id, activeCharacter?.id, isPauseRecord]);

    // Handle hotkeys
    useEffect(() => {
        const handlePressShiftEnter = () => {
            if (sentenceProcess !== SentenceProcessEnum.FINISH || state.isSendingRecord) return;
            handeBackSentence(BackActionEnum.START);
        };

        const handlePressSpace = () => {

            if (sentence?.character_id === activeCharacter?.id) {
                toggleRecording();
            } else {
                console.log('handlePressSpace', state.isPauseSpeak);
                dispatch({ type: 'SET_PAUSE_SPEAK', payload: !state.isPauseSpeak });
            }
        };

        const handleEnterEvent = () => {

            if (state.isSendingRecord || !activeCharacter) return;
            if (state.isCountDown) {
                dispatch({ type: 'SET_SHOW_COUNT_DOWN', payload: false });
                return;
            }
            if (state.isBackAction) dispatch({ type: 'SET_BACK_ACTION', payload: false });
            if (!state.isNextAction) dispatch({ type: 'SET_NEXT_ACTION', payload: true });
            console.log('handleEnterEvent', sentenceProcess, recordProcess, state.isBackAction, activeCharacter.id, sentence?.id);
            if(state.isPauseSpeak){
                dispatch({ type: 'SET_PAUSE_SPEAK', payload: false });
            }
            if (activeCharacter.id !== sentence?.character_id) {
                if (sentenceProcess === SentenceProcessEnum.FINISH || speakings.length === 0) {
                    setSentenceProcess(SentenceProcessEnum.FINISH);
                    handleNextSentence();
                }
                if (conversations.length === speakings.length) {
                    setFinishLearn(true);
                }
                return;
            }

            switch (sentenceProcess) {
                case SentenceProcessEnum.START:
                    setSentenceProcess(SentenceProcessEnum.PROCESS);
                    console.log('SentenceProcessEnum.START', recordProcess, isPauseRecord, sentenceProcess);
                    if (recordProcess === RecordProcessEnum.INIT) {
                        setRecordProcess(RecordProcessEnum.PROCESS);
                    }
                    break;

                case SentenceProcessEnum.PROCESS:
                    if (recordProcess === RecordProcessEnum.PROCESS) {
                        delay(100);
                        stopMicrophone(false);
                        break;
                    }
                    if (recordProcess === RecordProcessEnum.INIT) {
                        setRecordProcess(RecordProcessEnum.PROCESS);
                    }
                    break;

                case SentenceProcessEnum.FINISH:
                    if (conversations.length === speakings.length) {
                        setFinishLearn(true);
                        break;
                    }
                    if (recordProcess === RecordProcessEnum.PROCESS) {
                        delay(100);
                        stopMicrophone(false).then(() => {
                            handleNextSentence();
                        });
                    } else {
                        handleNextSentence();
                    }
                    break;
            }
        };

        hotkeys('shift+enter', handlePressShiftEnter);
        hotkeys('left', () => handeBackSentence(BackActionEnum.PREV));
        hotkeys('space', handlePressSpace);
        hotkeys('enter', handleEnterEvent);

        return () => {
            hotkeys.unbind('shift+enter');
            hotkeys.unbind('left');
            hotkeys.unbind('space');
            hotkeys.unbind('enter');
        };
    });

    // Helper functions for external use
    const setIsPauseSpeak = useCallback((value: boolean) => {
        dispatch({ type: 'SET_PAUSE_SPEAK', payload: value });
    }, []);

    // Return all necessary states and actions
    return {
        // States
        speakings,
        isFinishLearn,
        activeCharacter,
        exerciseToken,
        isPauseRecord,
        isPauseSpeak,
        isNextAction,
        isBackAction,
        isCountDown,
        showTooltipRecord,
        sentenceProcess,
        isSendingRecord,
        isStartLearn: state.isStartLearn,
        ref,
        handeBackSentence,
        handleStartLearn,
        handleFinishCountDown,
        toggleRecording,
        setIsPauseSpeak,
        setCharacter,
        stopMicrophone,
        setIsStartLearn: (value: boolean) => dispatch({ type: 'SET_START_LEARN', payload: value }),
    };
}
