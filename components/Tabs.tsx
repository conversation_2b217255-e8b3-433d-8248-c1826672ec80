'use client';

import React from 'react';

import { Tab, Tabs as UiTabs, extendVariants } from '@heroui/react';
import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { TabsProps } from 'types/tabs';

import { Tooltip } from '@/components/Tooltip';

const TabsStyle = extendVariants(UiTabs, {
  variants: {
    color: {
      default: {
        base: 'h-[25px] p-0 relative z-10 transition-all duration-200',
        tabList:
          'min-h-6 h-auto gap-0 p-0 bg-bg-box border overflow-hidden border-color-border rounded-md transition-all duration-200',
        tab: 'h-[25px] text-color-minor px-4 after:border-none -mr-0.5 bg-transparent hover:bg-bg-box transition-all duration-200',
        cursor:
          'bg-bg-general dark:bg-bg-general border border-color-border rounded-md transition-all duration-200',
        tabContent:
          'text-color-minor hover:text-color-major h-[25px] flex items-center text-medium whitespace-nowrap cursor-pointer rounded-md w-full text-normal font-medium transition-all duration-200',
        panel: 'transition-all duration-200',
      },
    },
    isDisabled: {},
    size: {},
    base: '',
  },
  defaultVariants: {
    color: 'default',
    size: 'sm',
    rounded: 'sm',
  },
  compoundVariants: [],
});

type CustomTabsProps = TabsProps & {
  classNames?: {
    tab?: string;
  };
  onSelectionChange?: (key: React.Key) => void;
};

const tabAnimation = {
  initial: { opacity: 0, y: -10 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: 10 },
  transition: { duration: 0.2 },
};

export default function Tabs({
  activeTabId,
  tabs,
  onClick,
  showTooltip = false,
  value = 'id',
  classNames,
  ...props
}: CustomTabsProps) {
  const t = useTranslations();

  if (tabs.length === 0) return null;

  const handleSelectionChange = (key: React.Key) => {
    if (onClick) {
      onClick(key.toString());
    } else if (props.onSelectionChange) {
      props.onSelectionChange(key);
    }
  };

  return (
    <TabsStyle
      selectedKey={activeTabId?.toString()}
      onSelectionChange={handleSelectionChange}
      classNames={classNames}
      {...props}
    >
      {tabs?.map((tab) => {
        const tabId = tab[value];
        return (
          <Tab
            key={tabId.toString()}
            id={tabId.toString()}
            title={
              <motion.div
                key={tabId.toString()}
                initial="initial"
                animate="animate"
                exit="exit"
                variants={tabAnimation}
              >
                <Tooltip
                  color="foreground"
                  showArrow={true}
                  content={tab.label || tabId.toString()}
                  isDisabled={!showTooltip}
                  placement="bottom"
                >
                  <div className="h-full flex">{tab.labelKey ? t(tab.labelKey) : tab.title}</div>
                </Tooltip>
              </motion.div>
            }
            isDisabled={tab.disabled}
          />
        );
      })}
    </TabsStyle>
  );
}
