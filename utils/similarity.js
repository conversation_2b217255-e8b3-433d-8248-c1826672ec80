const fuzzball = require('fuzzball'); // Import the fuzzball module

/**
 * Calculates the similarity score between two strings using fuzzball.ratio.
 *
 * @param {string} content The first string.
 * @param {string} template The second string.
 * @returns {number} the similarity score (0-100).
 */
function similarity(content, template) {
  const lowerContent = content.toLowerCase();
  const lowerTemplate = template.toLowerCase();
  return fuzzball.ratio(lowerContent, lowerTemplate);
}
module.exports = similarity;
