{"name": "e-learning", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack", "dev1": "NODE_OPTIONS='--inspect' next dev --turbopack", "build": "NODE_ENV=production next build", "start": "NODE_ENV=production node index.js", "type-check": "tsc --project tsconfig.json", "tsc": "tsc --noEmit", "lint": "next lint", "fix": "next lint --fix", "format": "prettier --write .", "prepare": "husky"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@heroui/react": "2.6.14", "@heroui/system": "^2.4.7", "@heroui/use-infinite-scroll": "^2.2.7", "@heroui/use-theme": "^2.1.2", "@hookform/resolvers": "^2.9.11", "@internationalized/date": "3.6.0", "@popperjs/core": "^2.11.8", "@react-aria/i18n": "3.12.4", "@tanstack/react-query": "^5.66.7", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/cookies": "^0.7.7", "@types/http-proxy": "^1.17.11", "@types/react-dom": "19.1.3", "axios": "1.8.2", "boring-avatars": "^1.10.1", "classnames": "^2.3.2", "cookies": "^0.8.0", "cookies-next": "^2.1.2", "date-fns": "^3.6.0", "dnd-core": "^16.0.1", "framer-motion": "^11.15.0", "franc": "^6.2.0", "fs": "0.0.1-security", "fuzzball": "^2.2.2", "hotkeys-js": "^3.13.10", "howler": "^2.2.4", "http-proxy": "^1.18.1", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "moment": "^2.30.1", "next": "15.2.4", "next-auth": "5.0.0-beta.25", "next-intl": "^3.26.3", "next-themes": "^0.4.4", "prettier-plugin-tailwindcss": "^0.6.11", "prop-types": "^15.8.1", "qs": "^6.11.2", "react": "19.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "19.0.0", "react-drag-drop-files": "^2.4.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.4.1", "react-hotkeys-hook": "^4.6.1", "react-idle-timer": "^5.7.2", "react-infinite-scroll-component": "^6.1.0", "react-otp-input": "^3.1.1", "react-photo-view": "^1.2.7", "react-router-dom": "^6.24.1", "react-use-audio-player": "^2.2.0", "recharts": "^2.15.1", "sharp": "0.32.6", "smartcrop": "^2.0.5", "swr": "^2.3.0", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.17", "usehooks-ts": "^3.1.1", "wavesurfer.js": "^7.9.1", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@commitlint/cz-commitlint": "^19.6.1", "@commitlint/types": "^19.5.0", "@eslint/eslintrc": "^3.2.0", "@next/eslint-plugin-next": "^15.1.6", "@tailwindcss/line-clamp": "^0.4.4", "@types/lodash": "^4.14.200", "@types/node": "^20.11.0", "@types/numeral": "^2.0.2", "@types/qs": "^6.9.7", "@types/react": "^19.0.0", "@typescript-eslint/eslint-plugin": "^8.26.0", "@typescript-eslint/parser": "^8.25.0", "autoprefixer": "^10.4.14", "eslint": "^9.20.0", "eslint-config-next": "^15.1.7", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "husky": "^9.1.7", "lint-staged": "^15.2.1", "postcss": "^8.5.3", "prettier": "^3.5.2", "pretty-quick": "^4.0.0", "typescript": "5.7.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged && tsc --noEmit", "pre-push": "npm run type-check && npm test"}}}